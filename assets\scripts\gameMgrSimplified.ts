import { _decorator, Component, Node, Prefab, Button } from 'cc';
const { ccclass, property } = _decorator;
import { lp } from './loadPrefab';
import { cellSpacing, maxFruitType, minFruitType, levelNum } from './config';
import { gameData } from './gameData';
import { cellFixed } from './cellFixed';
import { AudioManager } from './audioManager';
import { ConnectionSystem } from './connectionSystem';
import { VibrationSystem } from './vibrationSystem';
import { SelectionLogicManager } from './selectionLogicManager';
import { deadlockDetector } from './deadlockDetector';
import { simpleToast } from './simpleToast';
import { LogManager } from './logManager';

/**
 * 精简版游戏管理器 - 主要负责游戏初始化和协调各个系统
 * 
 * <EMAIL>
 */
@ccclass('gameMgrSimplified')
export class gameMgrSimplified extends Component {
    
    // ==================== 预制体属性 ====================
    @property({type: Prefab})
    backgroundPrefab: Prefab = null;
    @property({type: Prefab})
    headerPrefab: Prefab = null;
    @property({type: Prefab})
    gridPrefab: Prefab = null;
    @property({type: Prefab})
    menuPrefab: Prefab = null;
    @property({type: Prefab})
    cellPrefab: Prefab = null;
    @property({type: Prefab})
    loadingPrefab: Prefab = null;

    // ==================== 游戏状态 ====================
    private gridNode: Node = null;
    private levelIndex: number = levelNum - 1;
    private _cachedLevel: any = null;  // 缓存当前关卡数据，提升性能
    
    // ==================== 系统管理器 ====================
    private audioManager: AudioManager = null;
    private connectionSystem: ConnectionSystem = null;
    private vibrationSystem: VibrationSystem = null;
    private selectionLogicManager: SelectionLogicManager = null;
    private deadlockDetector: deadlockDetector = null;
    private toastManager: simpleToast = null;
    
    // ==================== 颜色映射系统 ====================
    private static colorMapping: Map<number, number> = new Map();
    private static readonly BACKGROUND_COLOR_COUNT = 11;
    private static colorAssignmentCounter = 0;

    // ==================== 单例模式 ====================
    private static instance: gameMgrSimplified = null;

    /**
     * 获取游戏管理器单例实例
     */
    public static getInstance(): gameMgrSimplified {
        return gameMgrSimplified.instance;
    }

    // ==================== 生命周期方法 ====================
    
    start() {
        LogManager.gameManager.log("🚀 [游戏启动] 开始初始化游戏管理器");

        // 设置单例实例
        gameMgrSimplified.instance = this;
        LogManager.gameManager.log("✅ [单例模式] 游戏管理器实例已设置");

        // 初始化系统
        this.initializeSystems();

        // 加载Loading页面
        try {
            lp(this.loadingPrefab, this.node);
            LogManager.gameManager.log("✅ [界面加载] Loading界面加载完成");
        } catch (error) {
            LogManager.gameManager.error("❌ [界面加载] Loading界面加载失败", error);
        }
    }

    // ==================== 初始化方法 ====================

    /**
     * 进入游戏主界面（由loading界面调用）
     */
    enterGame() {
        LogManager.gameManager.log("🚀 [游戏进入] 开始进入游戏主界面");

        try {
            // 加载游戏主界面UI
            this.loadUI();
            LogManager.gameManager.log("✅ [UI加载] 游戏主界面UI加载完成");

            // 初始化游戏网格
            this.initGrid();
            LogManager.gameManager.log("✅ [棋盘初始化] 游戏棋盘初始化完成");

            // 开始游戏音频（立即开始，不延迟）
            this.startGameAudioImmediate();
            LogManager.gameManager.log("✅ [游戏进入] 游戏主界面进入完成");
        } catch (error) {
            LogManager.gameManager.error("❌ [游戏进入] 进入游戏主界面失败", error);
        }
    }

    /**
     * 优化的进入游戏方法（一次性加载所有内容）
     */
    enterGameOptimized() {
        // 一次性加载所有UI
        this.loadUI();

        // 立即初始化游戏网格
        this.initGrid();

        // 立即开始音频
        this.startGameAudioImmediate();
    }
    


    
    /**
     * 初始化所有系统
     */
    private initializeSystems() {
        LogManager.gameManager.log("🔧 [系统初始化] 开始初始化各个子系统");

        try {
            // 初始化音频管理器
            this.audioManager = AudioManager.getInstance();
            LogManager.gameManager.log("✅ [音频系统] 音频管理器初始化完成");

            // 初始化振动系统
            this.vibrationSystem = VibrationSystem.getInstance();
            this.vibrationSystem.initialize();
            LogManager.gameManager.log("✅ [振动系统] 振动系统初始化完成");

            // 初始化Toast管理器
            this.toastManager = this.node.addComponent(simpleToast);
            LogManager.gameManager.log("✅ [提示系统] Toast管理器初始化完成");

            // 初始化无解检测器
            this.deadlockDetector = this.node.addComponent(deadlockDetector);
            LogManager.gameManager.log("✅ [检测系统] 无解检测器初始化完成");

            LogManager.gameManager.log("✅ [系统初始化] 所有子系统初始化完成");
        } catch (error) {
            LogManager.gameManager.error("❌ [系统初始化] 子系统初始化失败", error);
        }
    }
    
    /**
     * 加载UI界面
     */
    private loadUI() {
        lp(this.backgroundPrefab, this.node);
        lp(this.headerPrefab, this.node);
        this.gridNode = lp(this.gridPrefab, this.node);
        lp(this.menuPrefab, this.node);
    }

    /**
     * 分步加载UI - 第1步：背景和头部
     */
    private loadUIStep1() {
        lp(this.backgroundPrefab, this.node);
        lp(this.headerPrefab, this.node);
    }

    /**
     * 分步加载UI - 第2步：网格和菜单
     */
    private loadUIStep2() {
        this.gridNode = lp(this.gridPrefab, this.node);
        lp(this.menuPrefab, this.node);
    }

    /**
     * 开始游戏音频（延迟版本）
     */
    private startGameAudio() {
        this.scheduleOnce(() => {
            if (this.audioManager) {
                this.audioManager.onGameStart();
            }
        }, 2.0);
    }

    /**
     * 立即开始游戏音频（无延迟版本）
     */
    private startGameAudioImmediate() {
        if (this.audioManager) {
            this.audioManager.onGameStart();
        }
    }

    // ==================== 棋盘初始化 ====================
    
    /**
     * 初始化棋盘
     */
    initGrid() {
        LogManager.gameManager.log("🚀 [棋盘初始化] 开始初始化游戏棋盘");

        try {
            // 清除缓存，确保使用最新数据
            this._cachedLevel = null;
            LogManager.gameManager.log("🔄 [缓存清理] 已清除关卡缓存数据");

            gameMgrSimplified.resetColorMapping();
            LogManager.gameManager.log("🎨 [颜色映射] 已重置颜色映射系统");

            const currentLevel = gameData[this.levelIndex];
            LogManager.gameManager.log(`📊 [关卡数据] 当前关卡: ${this.levelIndex + 1}, 棋盘大小: ${currentLevel.row}x${currentLevel.col}`);

            let cellCount = 0;
            let randomType = 0;

            // 批量创建棋子，减少单次创建的开销
            const cells = [];
            for (let i = 0; i < currentLevel.row; i++) {
                for (let j = 0; j < currentLevel.col; j++) {
                    if (cellCount % 2 === 0) {
                        randomType = Math.floor(Math.random() * (maxFruitType - minFruitType + 1)) + minFruitType;
                    }
                    cells.push({ row: i, col: j, type: randomType });
                    cellCount++;
                }
            }

            LogManager.gameManager.log(`🎲 [棋子生成] 已生成 ${cellCount} 个棋子，类型范围: ${minFruitType}-${maxFruitType}`);

            // 快速创建所有棋子
            cells.forEach(cellData => {
                this.createCell(cellData.row, cellData.col, cellData.type);
            });
            LogManager.gameManager.log("🎯 [棋子创建] 所有棋子创建完成");

            this.shuffleCells();
            LogManager.gameManager.log("🔀 [棋盘洗牌] 棋盘洗牌完成");

            // 初始化连接系统（需要在棋盘创建后）
            this.connectionSystem = new ConnectionSystem(this.levelIndex, this.gridNode);
            LogManager.gameManager.log("🔗 [连接系统] 连接检测系统初始化完成");

            // 初始化选择逻辑管理器
            this.selectionLogicManager = new SelectionLogicManager(this.connectionSystem, this.gridNode);
            LogManager.gameManager.log("👆 [选择系统] 选择逻辑管理器初始化完成");

            // 初始化无解检测器
            this.deadlockDetector.initialize(this.connectionSystem, this, this.toastManager);
            LogManager.gameManager.log("🔍 [检测系统] 无解检测器配置完成");

            // 延迟检测无解情况，避免阻塞主线程
            this.scheduleOnce(() => {
                this.deadlockDetector.checkForDeadlock(true);
            }, 0.1);

            LogManager.gameManager.log("✅ [棋盘初始化] 棋盘初始化完全完成");
        } catch (error) {
            LogManager.gameManager.error("❌ [棋盘初始化] 棋盘初始化失败", error);
        }
    }
    
    /**
     * 创建单个棋子（优化版本）
     */
    createCell(row: number, col: number, iconType: number) {
        // 快速创建节点
        const cellNode = lp(this.cellPrefab, this.gridNode);

        // 缓存当前关卡数据，避免重复访问
        if (!this._cachedLevel) {
            this._cachedLevel = gameData[this.levelIndex];
        }

        // 快速计算位置
        const x = (col - (this._cachedLevel.col - 1) / 2) * cellSpacing;
        const y = ((this._cachedLevel.row - 1) / 2 - row) * cellSpacing;
        cellNode.setPosition(x, y, 0);

        // 快速设置棋子类型
        const cellComp = cellNode.getComponent(cellFixed);
        if (cellComp) {
            cellComp.setType(iconType);
        }

        // 快速添加点击事件
        const button = cellNode.addComponent(Button);
        button.node.on(Button.EventType.CLICK, () => {
            this.onCellClick(cellNode);
        }, this);

        return cellNode;
    }
    
    /**
     * 洗牌算法
     * 重新随机排列棋盘上的棋子类型，保持类型数量和配对关系不变
     */
    public shuffleCells() {
        if (!this.gridNode || !this.gridNode.children) {
            console.warn("⚠️ 棋盘节点无效，无法执行洗牌");
            return;
        }

        const cellNodes = this.gridNode.children.filter(node => node.active && !node['isPathRenderer']);

        // 收集所有棋子的类型并统计
        const cellTypes: number[] = [];
        const beforeCounts: { [key: number]: number } = {};

        cellNodes.forEach(cellNode => {
            const cellComponent = cellNode.getComponent(cellFixed);
            if (cellComponent) {
                const type = cellComponent.getType();
                cellTypes.push(type);
                beforeCounts[type] = (beforeCounts[type] || 0) + 1;
            }
        });

        // 检查收集的类型数量
        if (cellTypes.length !== cellNodes.length) {
            return;
        }

        // 使用Fisher-Yates算法打乱类型数组
        for (let i = cellTypes.length - 1; i > 0; i--) {
            const randomIndex = Math.floor(Math.random() * (i + 1));
            // 交换数组中的元素
            [cellTypes[i], cellTypes[randomIndex]] = [cellTypes[randomIndex], cellTypes[i]];
        }

        // 将打乱后的类型重新分配给棋子
        cellNodes.forEach((cellNode, index) => {
            const cellComponent = cellNode.getComponent(cellFixed);
            if (cellComponent && index < cellTypes.length) {
                cellComponent.setType(cellTypes[index]);
            }
        });


    }

    // ==================== 游戏交互逻辑 ====================
    
    /**
     * 棋子点击处理
     */
    onCellClick(cellNode: Node) {
        LogManager.gameManager.log(`🖱️ [棋子点击] 处理棋子点击: ${cellNode.name}`);

        try {
            // 使用选择逻辑管理器处理点击
            this.selectionLogicManager.handleCellClick(cellNode, () => {
                // 棋子消除后检查游戏胜利
                this.checkGameWin();

                // 检查是否存在可连接的棋子对，如果不存在则自动打乱
                this.deadlockDetector.checkForDeadlock();
            });
        } catch (error) {
            LogManager.gameManager.error("❌ [棋子点击] 处理棋子点击失败", error);
        }
    }

    
    /**
     * 检查游戏胜利
     */
    private checkGameWin() {
        if (!this.gridNode) {
            LogManager.gameManager.warn("⚠️ [胜利检测] 棋盘节点不存在，跳过胜利检测");
            return;
        }

        // 统计还有多少活跃的棋子（排除路径渲染器节点）
        let activeCellCount = 0;
        this.gridNode.children.forEach(cellNode => {
            // 排除路径渲染器节点，只计算真正的棋子
            if (cellNode.active && !cellNode['isPathRenderer']) {
                activeCellCount++;
            }
        });

        LogManager.gameManager.log(`🔍 [胜利检测] 当前剩余棋子数量: ${activeCellCount}`);

        // 如果没有活跃的棋子，游戏胜利
        if (activeCellCount === 0) {
            LogManager.gameManager.log("🎉 [游戏胜利] 检测到游戏胜利条件，触发胜利处理");
            this.onGameWin();
        }
    }
    
    /**
     * 游戏胜利处理
     */
    private onGameWin() {
        LogManager.gameManager.log("🏆 [游戏胜利] 开始执行胜利处理流程");

        try {
            // 停止背景音乐并播放胜利音效
            if (this.audioManager) {
                this.audioManager.stopBGM();
                LogManager.gameManager.log("🔇 [音频控制] 已停止背景音乐");

                this.audioManager.playWinSound();
                LogManager.gameManager.log("🎵 [音频控制] 已播放胜利音效");
            } else {
                LogManager.gameManager.warn("⚠️ [音频控制] 音频管理器不可用，跳过音效播放");
            }

            // 播放胜利动画（暂时注释，因为用户已注释）
            //AnimationSystem.playVictoryAnimation(this.node);

            LogManager.gameManager.log("✅ [游戏胜利] 胜利处理流程完成");
        } catch (error) {
            LogManager.gameManager.error("❌ [游戏胜利] 胜利处理流程失败", error);
        }
    }

    // ==================== 颜色映射系统 ====================
    
    /**
     * 获取图标类型对应的背景颜色索引
     */
    public static getBackgroundColorIndex(iconType: number): number {
        if (gameMgrSimplified.colorMapping.has(iconType)) {
            return gameMgrSimplified.colorMapping.get(iconType);
        }
        
        const colorIndex = gameMgrSimplified.colorAssignmentCounter % gameMgrSimplified.BACKGROUND_COLOR_COUNT;
        gameMgrSimplified.colorMapping.set(iconType, colorIndex);
        gameMgrSimplified.colorAssignmentCounter++;
        
        return colorIndex;
    }
    
    /**
     * 重置颜色映射
     */
    public static resetColorMapping() {
        gameMgrSimplified.colorMapping.clear();
        gameMgrSimplified.colorAssignmentCounter = 0;
    }
}
